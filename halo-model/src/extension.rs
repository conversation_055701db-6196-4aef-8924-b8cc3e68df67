use crate::{GroupVersionKind, Metadata};
use std::cmp::Ordering;

/// ExtensionOperator包含Extension所需字段的getter和setter方法
pub trait ExtensionOperator {
    /// 获取API版本
    fn get_api_version(&self) -> &str;

    /// 获取类型名称
    fn get_kind(&self) -> &str;
    
    /// 获取元数据
    fn get_metadata(&self) -> &Metadata;

    /// 获取可变元数据引用
    fn get_metadata_mut(&mut self) -> &mut Metadata;

    /// 设置API版本
    fn set_api_version(&mut self, api_version: String);

    /// 设置类型名称
    fn set_kind(&mut self, kind: String);

    /// 设置元数据
    fn set_metadata(&mut self, metadata: Metadata);

    /// 获取GroupVersionKind
    fn group_version_kind(&self) -> GroupVersionKind;

    /// 设置GroupVersionKind
    fn set_group_version_kind(&mut self, gvk: GroupVersionKind);

    /// 检查Extension是否未被删除
    fn is_not_deleted(&self) -> bool {
        !self.is_deleted()
    }

    /// 检查Extension是否被删除
    fn is_deleted(&self) -> bool {
        self.get_metadata().is_deleted()
    }
}

/// Extension是一个表示Extension的trait，包含GroupVersionKind和Metadata的setter和getter
pub trait Extension: ExtensionOperator {
    /// 比较两个Extension
    fn compare_to(&self, other: &Self) -> Ordering
    where
        Self: Ord,
    {
        self.cmp(other)
    }
}

/// 用于检查Extension是否未被删除的谓词函数
pub fn is_not_deleted<T: ExtensionOperator>(extension: &T) -> bool {
    extension.is_not_deleted()
}

/// 用于检查Extension是否被删除的函数
pub fn is_deleted<T: ExtensionOperator>(extension: &T) -> bool {
    extension.is_deleted()
}
