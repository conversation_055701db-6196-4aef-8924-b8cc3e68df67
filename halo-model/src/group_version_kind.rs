use serde::{Deserialize, Serialize};

/// GroupVersion包含Extension的组和版本名称
#[derive(<PERSON>bu<PERSON>, <PERSON>lone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub struct GroupVersion {
    pub group: String,
    pub version: String,
}

impl GroupVersion {
    pub fn new(group: String, version: String) -> Self {
        Self { group, version }
    }

    /// 从API版本字符串解析GroupVersion
    /// 例如："core.halo.run/v1alpha1" -> GroupVersion { group: "core.halo.run", version: "v1alpha1" }
    /// 例如："v1alpha1" -> GroupVersion { group: "", version: "v1alpha1" }
    pub fn parse_api_version(api_version: &str) -> Self {
        if let Some(slash_pos) = api_version.rfind('/') {
            let group = api_version[..slash_pos].to_string();
            let version = api_version[slash_pos + 1..].to_string();
            Self::new(group, version)
        } else {
            Self::new(String::new(), api_version.to_string())
        }
    }

    /// 检查是否有组名
    pub fn has_group(&self) -> bool {
        !self.group.is_empty()
    }
}

impl std::fmt::Display for GroupVersion {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        if self.has_group() {
            write!(f, "{}/{}", self.group, self.version)
        } else {
            write!(f, "{}", self.version)
        }
    }
}

/// GroupKind包含Extension的组和类型名称
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub struct GroupKind {
    pub group: String,
    pub kind: String,
}

impl GroupKind {
    pub fn new(group: String, kind: String) -> Self {
        Self { group, kind }
    }

    /// 检查是否有组名
    pub fn has_group(&self) -> bool {
        !self.group.is_empty()
    }
}

impl std::fmt::Display for GroupKind {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        if self.has_group() {
            write!(f, "{}/{}", self.group, self.kind)
        } else {
            write!(f, "{}", self.kind)
        }
    }
}

/// GroupVersionKind包含Extension的组、版本和类型名称
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash, Default)]
pub struct GroupVersionKind {
    pub group: String,
    pub version: String,
    pub kind: String,
}

impl GroupVersionKind {
    /// 创建新的GroupVersionKind实例
    pub fn new(group: String, version: String, kind: String) -> Self {
        assert!(!version.is_empty(), "版本不能为空");
        assert!(!kind.is_empty(), "类型不能为空");

        Self { group, version, kind }
    }

    /// 获取GroupVersion
    pub fn group_version(&self) -> GroupVersion {
        GroupVersion::new(self.group.clone(), self.version.clone())
    }

    /// 获取GroupKind
    pub fn group_kind(&self) -> GroupKind {
        GroupKind::new(self.group.clone(), self.kind.clone())
    }

    /// 检查是否有组名
    pub fn has_group(&self) -> bool {
        !self.group.is_empty()
    }

    /// 从API版本和类型名称创建GroupVersionKind
    pub fn from_api_version_and_kind(api_version: &str, kind: &str) -> Self {
        assert!(!kind.is_empty(), "类型不能为空");

        let gv = GroupVersion::parse_api_version(api_version);
        Self::new(gv.group, gv.version, kind.to_string())
    }

    /// 获取API版本字符串
    pub fn api_version(&self) -> String {
        self.group_version().to_string()
    }

    /// 获取类型名称
    pub fn kind(&self) -> &str {
        &self.kind
    }
}

impl std::fmt::Display for GroupVersionKind {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        if self.has_group() {
            write!(f, "{}/{}/{}", self.group, self.version, self.kind)
        } else {
            write!(f, "{}/{}", self.version, self.kind)
        }
    }
}
