[server]
name = "rust-halo"
address = "0.0.0.0:5800"
ssl = false
[database]
# mysql://34rpxCBtHECBMBn.root:<PASSWORD>@gateway01.ap-southeast-1.prod.aws.tidbcloud.com:4000/test?sslMode=VERIFY_IDENTITY
database_url="mysql://34rpxCBtHECBMBn.root:<EMAIL>:4000/test?sslMode=VERIFY_IDENTITY"
[jwt]
jwt_secret = "secret"
jwt_exp = 6000
[cert]
cert = "config/certs/cert.pem"
key = "config/certs/key.pem"
[log]
filter_level = "info"        # "debug" "info" "warn" "error"
with_ansi = true
to_stdout = true
directory = "./logs"
file_name = "my-service.log"
rolling = "daily"            # "minutely" "hourly" "daily" "never"
