# Rust 依赖注入系统

一个为Rust项目设计的高性能、线程安全的依赖注入系统，不使用trait，支持多线程调用模式。

## 特性

- ✅ **无trait设计**: 使用具体类型，避免trait对象开销
- ✅ **多线程安全**: 使用Arc + RwLock保证并发安全
- ✅ **类型安全**: 编译时类型检查，避免运行时错误
- ✅ **生命周期管理**: 支持单例和瞬态服务
- ✅ **高性能**: 零成本抽象，高效内存使用
- ✅ **易于使用**: 简洁的API，清晰的使用模式

## 快速开始

### 1. 基本使用

```rust
use std::sync::Arc;
use crate::di::{ServiceContainer, services::ConfigService};

// 创建容器
let container = ServiceContainer::new();

// 注册单例服务
container.register_singleton::<ConfigService, _>(|_| {
    Ok(ConfigService::new(config))
})?;

// 注册依赖其他服务的服务
container.register_singleton::<UserService, _>(|container| {
    let config_service = container.resolve::<ConfigService>()?;
    Ok(UserService::new(config_service))
})?;

// 解析服务
let user_service = container.resolve::<UserService>()?;
```

### 2. 应用集成

```rust
// 在应用启动时初始化
async fn init_app() -> Result<()> {
    let db_connection = get_database_connection().await?;
    let config = load_config()?;
    
    // 初始化全局容器
    AppContainer::initialize(db_connection, config).await?;
    Ok(())
}

// 在业务代码中使用
#[handler]
pub async fn get_user_info() -> Result<Json<UserInfo>> {
    let user_service = AppContainer::get_user_service()?;
    let user = user_service.get_current_user().await?;
    Ok(Json(user))
}
```

### 3. 自定义服务

```rust
// 定义服务结构
#[derive(Debug)]
pub struct MyService {
    config: Arc<ConfigService>,
    database: Arc<DatabaseService>,
}

impl MyService {
    pub fn new(
        config: Arc<ConfigService>, 
        database: Arc<DatabaseService>
    ) -> Self {
        Self { config, database }
    }
    
    pub async fn do_something(&self) -> Result<String> {
        // 使用注入的依赖
        let server_name = &self.config.config().server.name;
        // ... 业务逻辑
        Ok(format!("Hello from {}", server_name))
    }
}

// 注册服务
container.register_singleton::<MyService, _>(|container| {
    let config = container.resolve::<ConfigService>()?;
    let database = container.resolve::<DatabaseService>()?;
    Ok(MyService::new(config, database))
})?;
```

## 核心概念

### 服务生命周期

#### 单例 (Singleton)
```rust
// 整个应用生命周期内只有一个实例
container.register_singleton::<ConfigService, _>(|_| {
    Ok(ConfigService::new(config))
})?;

let service1 = container.resolve::<ConfigService>()?;
let service2 = container.resolve::<ConfigService>()?;
// service1 和 service2 是同一个实例
assert!(Arc::ptr_eq(&service1, &service2));
```

#### 瞬态 (Transient)
```rust
// 每次解析都创建新实例
container.register_transient::<LogService, _>(|_| {
    Ok(LogService::new())
})?;

let service1 = container.resolve::<LogService>()?;
let service2 = container.resolve::<LogService>()?;
// service1 和 service2 是不同的实例
assert!(!Arc::ptr_eq(&service1, &service2));
```

### 依赖注入

```rust
// 服务A依赖配置服务
container.register_singleton::<ServiceA, _>(|container| {
    let config = container.resolve::<ConfigService>()?;
    Ok(ServiceA::new(config))
})?;

// 服务B依赖服务A和配置服务
container.register_singleton::<ServiceB, _>(|container| {
    let service_a = container.resolve::<ServiceA>()?;
    let config = container.resolve::<ConfigService>()?;
    Ok(ServiceB::new(service_a, config))
})?;
```

## 多线程使用

```rust
use std::sync::Arc;
use tokio::task;

let container = Arc::new(ServiceContainer::new());

// 注册服务
container.register_singleton::<ConfigService, _>(|_| {
    Ok(ConfigService::new(config))
})?;

// 创建多个并发任务
let mut handles = Vec::new();
for i in 0..10 {
    let container_clone = Arc::clone(&container);
    let handle = task::spawn(async move {
        // 在不同线程中安全地解析服务
        let config_service = container_clone.resolve::<ConfigService>()?;
        // 使用服务...
        Ok(i)
    });
    handles.push(handle);
}

// 等待所有任务完成
for handle in handles {
    handle.await??;
}
```

## 错误处理

```rust
// 服务未注册
match container.resolve::<UnregisteredService>() {
    Ok(service) => { /* 使用服务 */ },
    Err(e) => {
        eprintln!("服务解析失败: {}", e);
        // 处理错误
    }
}

// 依赖解析失败
container.register_singleton::<ServiceWithDeps, _>(|container| {
    let dependency = container.resolve::<Dependency>()?; // 可能失败
    Ok(ServiceWithDeps::new(dependency))
})?;
```

## 最佳实践

### 1. 服务设计
- 使用具体类型而非trait
- 通过构造函数注入依赖
- 保持服务无状态或状态不可变
- 使用Arc包装依赖以支持多线程

### 2. 容器管理
- 在应用启动时一次性注册所有服务
- 使用全局容器管理应用级服务
- 为测试创建独立的容器实例

### 3. 错误处理
- 在服务工厂函数中处理依赖解析错误
- 使用Result类型传播错误
- 提供有意义的错误信息

### 4. 性能优化
- 优先使用单例服务减少内存分配
- 避免在热路径上频繁解析瞬态服务
- 缓存经常使用的服务引用

## 运行演示

```bash
# 运行依赖注入演示
cargo test demo --bin rust-halo

# 运行简单测试
cargo test simple_test --bin rust-halo

# 运行所有DI相关测试
cargo test di --bin rust-halo
```

## 项目结构

```
src/di/
├── mod.rs              # 模块导出
├── container.rs        # 核心容器实现
├── services.rs         # 预定义服务
├── app_container.rs    # 应用容器
├── examples.rs         # 使用示例
├── demo.rs            # 演示程序
├── tests.rs           # 单元测试
└── simple_test.rs     # 简化测试

src/services/
├── user_di.rs         # 用户服务DI版本
└── role_di.rs         # 角色服务DI版本
```

## 与其他DI框架对比

| 特性 | 本系统 | 其他trait-based系统 |
|------|--------|-------------------|
| 运行时开销 | 零成本抽象 | trait对象开销 |
| 编译时检查 | 完全类型安全 | 部分类型擦除 |
| 多线程安全 | Arc + RwLock | 依赖实现 |
| 学习曲线 | 简单直观 | 需要理解trait |
| 性能 | 高 | 中等 |

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更多信息

- [详细实现文档](DEPENDENCY_INJECTION_SUMMARY.md)
- [API文档](docs/api.md)
- [性能测试报告](docs/performance.md)
