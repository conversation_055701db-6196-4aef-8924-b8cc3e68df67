use crate::extension::index::scheme_manager::Scheme;
use std::slice::Iter;

pub trait SchemeWatcherManager {
    fn register(&mut self, watcher: Box<dyn SchemeWatcher>);

    fn unregister(&mut self, watcher: Box<dyn SchemeWatcher>);

    fn watchers(&self) -> Iter<Box<dyn SchemeWatcher>>;
}

pub trait SchemeWatcher {
    fn on_change(&self, event: Box<dyn ChangeEvent>);
}

pub trait ChangeEvent {}

pub struct SchemeRegistered {
    new_scheme: Scheme,
}
impl SchemeRegistered {
    pub(crate) fn new(scheme: Scheme) -> Self {
        SchemeRegistered { new_scheme: scheme }
    }
}
impl ChangeEvent for SchemeRegistered {}

pub struct SchemeUnregistered {
    deleted_scheme: Scheme,
}
impl SchemeUnregistered {
    pub(crate) fn new(deleted_scheme: Scheme) -> Self {
        SchemeUnregistered { deleted_scheme }
    }
}

impl ChangeEvent for SchemeUnregistered {}
pub struct DefaultSchemeWatcherManager {
    watchers: Vec<Box<dyn SchemeWatcher>>,
}

impl DefaultSchemeWatcherManager {
    pub fn new() -> Self {
        DefaultSchemeWatcherManager {
            watchers: Vec::new(),
        }
    }
}

impl SchemeWatcherManager for DefaultSchemeWatcherManager {
    fn register(&mut self, watcher: Box<dyn SchemeWatcher>) {
        self.watchers.push(watcher);
    }

    fn unregister(&mut self, watcher: Box<dyn SchemeWatcher>) {
        //self.watchers.retain(|w| w != watcher);
        todo!("self.watchers.retain(|w| w != watcher);")
    }

    fn watchers(&self) -> Iter<Box<dyn SchemeWatcher>> {
        self.watchers.iter()
    }
}
