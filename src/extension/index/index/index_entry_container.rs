use crate::config::app_error::AppResult;
use crate::extension::index::index::query::index_descriptor::IndexDescriptor;
use crate::extension::index::index::query::index_entry::IndexEntry;
use std::cell::RefCell;
use std::collections::HashMap;
use std::sync::Arc;

pub struct IndexEntryContainer<T>
where
    T: IndexEntry,
{
    index_entries: HashMap<String, Arc<RefCell<T>>>,
}

impl<IE> IndexEntryContainer<IE>
where
    IE: IndexEntry,
{
    pub fn new() -> Self {
        IndexEntryContainer {
            index_entries: HashMap::new(),
        }
    }

    /**
     * Add an `IndexEntry` to this container.
     *
     * @param entry the entry to add
     * @throws if the entry already exists
     */
    pub fn add(&mut self, index_entry: Arc<RefCell<IE>>) -> AppResult<()> {
        let index_descriptor = index_entry.borrow().get_index_descriptor().identity();
        if self.index_entries.contains_key(&index_descriptor) {
            return Err(
                anyhow::anyhow!("Index entry already exists for {:?}", index_descriptor).into(),
            );
        }
        self.index_entries.insert(index_descriptor, index_entry);
        Ok(())
    }

    /**
     * Get the `IndexEntry` for the given `IndexDescriptor`.
     *
     * @param index_descriptor the index descriptor
     * @return the index entry
     */
    pub fn get(&self, index_descriptor: &IndexDescriptor<IE::T>) -> Option<&Arc<RefCell<IE>>> {
        self.index_entries.get(&index_descriptor.identity())
    }

    pub fn contains(&self, index_descriptor: &IndexDescriptor<IE::T>) -> bool {
        self.index_entries
            .contains_key(&index_descriptor.identity())
    }

    pub fn remove(
        &mut self,
        index_descriptor: &IndexDescriptor<IE::T>,
    ) -> Option<Arc<RefCell<IE>>> {
        self.index_entries.remove(&index_descriptor.identity())
    }

    pub fn size(&self) -> usize {
        self.index_entries.len()
    }

    pub fn for_each<F>(&self, f: F)
    where
        F: Fn(&Arc<RefCell<IE>>),
    {
        self.index_entries.iter().for_each(|(_, v)| f(v));
    }

    pub fn iter(&self) -> impl Iterator<Item = &Arc<RefCell<IE>>> {
        self.index_entries.values()
    }

    pub fn remove_fn<F>(&mut self, f: F) -> Vec<Arc<RefCell<IE>>>
    where
        F: Fn(&IndexDescriptor<IE::T>) -> bool,
    {
        let to_remove = self
            .index_entries
            .iter()
            .filter_map(|(key, index_entry)| {
                let index_entry = index_entry.borrow_mut();
                if f(index_entry.get_index_descriptor()) {
                    Some(key.to_owned())
                } else {
                    None
                }
            })
            .collect::<Vec<String>>();

        let mut vec = Vec::new();
        for key in to_remove {
            if let Some(index_entry) = self.index_entries.remove(&key) {
                vec.push(index_entry.clone());
            }
        }
        vec
    }
}

#[cfg(test)]
mod tests {
    use crate::{
        extension::index::index::query::index_entry::IndexEntryImpl, tests::primary_key_index_spec,
    };use super::*;

    #[test]
    fn add() {
        let mut container = IndexEntryContainer::new();

        let spec = primary_key_index_spec();
        let descriptor = IndexDescriptor::new(spec);
        let entry = IndexEntryImpl::new(descriptor);

        let entry = Arc::new(RefCell::new(entry));
        container.add(entry.clone()).unwrap();

        let spec = primary_key_index_spec();
        let descriptor = IndexDescriptor::new(spec);
        assert!(container.contains(&descriptor));

        let result = container.add(entry.clone());
        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err().to_string(),
            format!(
                "error:`Index entry already exists for {:?}`",
                descriptor.identity()
            )
        );
    }

    #[test]
    fn remove() {
        let mut container = IndexEntryContainer::new();

        let spec = primary_key_index_spec();
        let descriptor = IndexDescriptor::new(spec);
        let entry = IndexEntryImpl::new(descriptor);

        let entry = Arc::new(RefCell::new(entry));
        container.add(entry.clone()).unwrap();

        let spec = primary_key_index_spec();
        let descriptor = IndexDescriptor::new(spec);
        assert!(container.contains(&descriptor));
        assert_eq!(container.size(), 1);

        container.remove(&descriptor).unwrap();

        assert!(!container.contains(&descriptor));
        assert_eq!(container.size(), 0);
    }
}
