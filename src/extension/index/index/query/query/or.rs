use crate::config::app_error::AppResult;
use crate::extension::index::index::indexer::DefaultIndexer;
use crate::extension::index::index::query::query::query_factory::Query;
use crate::extension::index::index::query::query::{BaseQuery, QueryIndexView};
use halo_model::ExtensionOperator;
use ordermap::OrderSet;

pub(crate) struct Or {
    pub child_queries: Vec<Query>,
    pub size: usize,
}

impl Or {
    pub fn new(child_queries: Vec<Query>) -> Self {
        let size = child_queries.len();
        Or {
            child_queries,
            size,
        }
    }
}

impl<T: ExtensionOperator> BaseQuery<T> for Or {
    fn matches(
        &self,
        index_view: &QueryIndexView<DefaultIndexer<T>>,
    ) -> AppResult<OrderSet<String>> {
        let mut all_names = OrderSet::new();
        for x in &self.child_queries {
            let set = x.matches(index_view)?;
            all_names.extend(set);
        }
        Ok(all_names)
    }
}
