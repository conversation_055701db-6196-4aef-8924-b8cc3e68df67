use crate::config::app_error::AppResult;
use crate::extension::index::index::index_entry_operator::IndexEntryOperator;
use crate::extension::index::index::indexer::DefaultIndexer;
use crate::extension::index::index::query::query::{BaseQuery, QueryIndexView};
use halo_model::ExtensionOperator;
use ordermap::OrderSet;

pub(crate) struct In {
    pub field_name: String,
    pub is_field_ref: bool,
    pub values: OrderSet<String>,
}

impl In {
    pub fn new(column_name: String, values: OrderSet<String>) -> Self {
        In {
            field_name: column_name,
            is_field_ref: false,
            values,
        }
    }
}

impl<T: ExtensionOperator> BaseQuery<T> for In {
    fn matches(
        &self,
        index_view: &QueryIndexView<DefaultIndexer<T>>,
    ) -> AppResult<OrderSet<String>> {
        let index_entry = index_view.get_index_entry(&self.field_name)?;
        let operator = IndexEntryOperator::new(index_entry);
        Ok(operator.find_in(&self.values))
    }
}
