use crate::config::app_error::AppResult;
use crate::extension::index::index::indexer::DefaultIndexer;
use crate::extension::index::index::query::query::{BaseQuery, QueryIndexView};
use halo_model::ExtensionOperator;
use ordermap::OrderSet;

pub(crate) struct Between {
    pub field_name: String,
    pub lower_value: String,
    pub lower_inclusive: bool,
    pub upper_value: String,
    pub upper_inclusive: bool,
}

impl Between {
    pub fn new(
        field_name: String,
        lower_value: String,
        lower_inclusive: bool,
        upper_value: String,
        upper_inclusive: bool,
    ) -> Self {
        Between {
            field_name,
            lower_value,
            lower_inclusive,
            upper_value,
            upper_inclusive,
        }
    }
}

impl<T: ExtensionOperator> BaseQuery<T> for Between {
    fn matches(
        &self,
        index_view: &QueryIndexView<DefaultIndexer<T>>,
    ) -> AppResult<OrderSet<String>> {
        index_view.between(
            &self.field_name,
            &self.lower_value,
            self.lower_inclusive,
            &self.upper_value,
            self.upper_inclusive,
        )
    }
}
