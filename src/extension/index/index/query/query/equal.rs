use crate::config::app_error::AppResult;
use crate::extension::index::index::indexer::DefaultIndexer;
use crate::extension::index::index::query::query::{BaseQuery, QueryIndexView};
use halo_model::ExtensionOperator;
use ordermap::OrderSet;

pub(crate) struct Equal {
    pub field_name: String,
    pub value: String,
    pub is_field_ref: bool,
}

impl Equal {
    pub fn new(field_name: String, value: String) -> Self {
        Equal {
            field_name,
            value,
            is_field_ref: false,
        }
    }

    pub fn new_all(field_name: String, value: String, is_field_ref: bool) -> Self {
        Equal {
            field_name,
            value,
            is_field_ref,
        }
    }
}

impl<T: ExtensionOperator> BaseQuery<T> for Equal {
    fn matches(
        &self,
        index_view: &QueryIndexView<DefaultIndexer<T>>,
    ) -> AppResult<OrderSet<String>> {
        if self.is_field_ref {
            return index_view.find_matching_ids_with_equal_values(&self.field_name, &self.value);
        }
        index_view.find_ids(&self.field_name, &self.value)
    }
}
