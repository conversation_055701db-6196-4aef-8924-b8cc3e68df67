use crate::config::app_error::AppResult;
use crate::extension::index::index::indexer::DefaultIndexer;
use crate::extension::index::index::query::query::{BaseQuery, QueryIndexView};
use halo_model::ExtensionOperator;
use ordermap::OrderSet;

pub(crate) struct IsNull {
    pub field_name: String,
    pub value: String,
    pub is_field_ref: bool,
}

impl IsNull {
    pub fn new(field_name: String) -> Self {
        IsNull {
            field_name,
            value: "".to_string(),
            is_field_ref: false,
        }
    }
}

impl<T: ExtensionOperator> BaseQuery<T> for IsNull {
    fn matches(
        &self,
        index_view: &QueryIndexView<DefaultIndexer<T>>,
    ) -> AppResult<OrderSet<String>> {
        let mut all_ids = index_view.get_all_ids()?;
        let ids_for_notnull = index_view.get_ids_for_field(&self.field_name)?;
        ids_for_notnull.iter().for_each(|id| {
            all_ids.remove(id);
        });
        Ok(all_ids)
    }
}
