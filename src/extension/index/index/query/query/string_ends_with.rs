use crate::config::app_error::AppResult;
use crate::extension::index::index::indexer::DefaultIndexer;
use crate::extension::index::index::query::index_entry::IndexEntry;
use crate::extension::index::index::query::query::{BaseQuery, QueryIndexView};
use halo_model::ExtensionOperator;
use ordermap::OrderSet;

pub(crate) struct StringEndsWith {
    pub field_name: String,
    pub value: String,
    pub is_field_ref: bool,
}

impl StringEndsWith {
    pub fn new(field_name: String, value: String) -> Self {
        StringEndsWith {
            field_name,
            value,
            is_field_ref: false,
        }
    }
}

impl<T: ExtensionOperator> BaseQuery<T> for StringEndsWith {
    fn matches(
        &self,
        index_view: &QueryIndexView<DefaultIndexer<T>>,
    ) -> AppResult<OrderSet<String>> {
        let mut result_set = OrderSet::new();
        let index_entry = index_view.get_index_entry(&self.field_name.clone())?;
        for (k, v) in index_entry.borrow().entries() {
            if k.ends_with(&self.value) {
                result_set.insert(v.clone());
            }
        }
        Ok(result_set)
    }
}
