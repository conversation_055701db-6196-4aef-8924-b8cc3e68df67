use crate::config::app_error::AppResult;
use crate::extension::index::index::indexer::DefaultIndexer;
use crate::extension::index::index::query::query::{BaseQuery, QueryIndexView};
use halo_model::ExtensionOperator;
use ordermap::OrderSet;

pub(crate) struct IsNotNull {
    pub field_name: String,
    pub value: String,
    pub is_field_ref: bool,
}

impl IsNotNull {
    pub fn new(field_name: String) -> Self {
        IsNotNull {
            field_name,
            value: "".to_string(),
            is_field_ref: false,
        }
    }
}

impl<T: ExtensionOperator> BaseQuery<T> for IsNotNull {
    fn matches(
        &self,
        index_view: &QueryIndexView<DefaultIndexer<T>>,
    ) -> AppResult<OrderSet<String>> {
        index_view.get_ids_for_field(&self.field_name)
    }
}
