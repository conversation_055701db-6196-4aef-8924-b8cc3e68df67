use crate::config::app_error::AppResult;
use crate::extension::index::index::indexer::DefaultIndexer;
use crate::extension::index::index::query::query::{BaseQuery, QueryIndexView};
use halo_model::ExtensionOperator;
use ordermap::OrderSet;

pub(crate) struct LessThan {
    pub field_name: String,
    pub value: String,
    pub is_field_ref: bool,
    pub or_equal: bool,
}

impl LessThan {
    pub fn new(field_name: String, value: String, or_equal: bool) -> Self {
        LessThan {
            field_name,
            value,
            is_field_ref: false,
            or_equal,
        }
    }

    pub fn new_all(field_name: String, value: String, or_equal: bool, is_field_ref: bool) -> Self {
        LessThan {
            field_name,
            value,
            is_field_ref,
            or_equal,
        }
    }
}

impl<T: ExtensionOperator> BaseQuery<T> for LessThan {
    fn matches(
        &self,
        index_view: &QueryIndexView<DefaultIndexer<T>>,
    ) -> AppResult<OrderSet<String>> {
        if self.is_field_ref {
            return index_view.find_matching_ids_with_smaller_values(
                &self.field_name,
                &self.value,
                self.or_equal,
            );
        }
        index_view.find_ids_less_than(&self.field_name, &self.value, self.or_equal)
    }
}
