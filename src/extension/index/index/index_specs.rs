use crate::config::app_error::AppResult;
use crate::extension::index::index::query::index_attribute::IndexAttribute;
use crate::extension::index::index::query::index_spec::IndexSpec;
use halo_model::ExtensionOperator;
use std::collections::HashMap;

/// An interface that defines a collection of [`IndexSpec`], and provides methods to add,
///  remove, and get [`IndexSpec`].
///
/// [`IndexSpec`]: crate::extension::index::index::query::index_spec::IndexSpec
pub trait IndexSpecs<T: ExtensionOperator> {
    fn add(&mut self, index_spec: IndexSpec<T>) -> AppResult<()>;

    fn get_index_specs(self) -> Vec<IndexSpec<T>>;

    fn get_index_spec(&self, index_name: String) -> Option<&IndexSpec<T>>;

    fn contains(&self, index_name: &String) -> bool;

    fn remove(&mut self, index_name: &String);
}

pub struct DefaultIndexSpecs<T: ExtensionOperator> {
    index_specs: HashMap<String, IndexSpec<T>>,
}

impl<T: ExtensionOperator> DefaultIndexSpecs<T> {
    pub fn new() -> Self {
        DefaultIndexSpecs {
            index_specs: HashMap::new(),
        }
    }

    fn check_index_spec(spec: &IndexSpec<T>) -> AppResult<()> {
        if spec.name.is_empty() {
            return Err(anyhow::anyhow!("IndexSpec name must not be blank").into());
        }
        Ok(())
    }
}

impl<T: ExtensionOperator> IndexSpecs<T> for DefaultIndexSpecs<T> {
    fn add(&mut self, index_spec: IndexSpec<T>) -> AppResult<()> {
        DefaultIndexSpecs::check_index_spec(&index_spec)?;
        let index_name = index_spec.name.clone();
        if let Some(existing_spec) = self.index_specs.insert(index_name, index_spec) {
            return Err(anyhow::anyhow!(
                "IndexSpec with name {} already exists",
                existing_spec.name
            )
            .into());
        }
        Ok(())
    }

    fn get_index_specs(self) -> Vec<IndexSpec<T>> {
        let mut vec = Vec::new();
        for (_, v) in self.index_specs {
            vec.push(v);
        }
        vec
    }

    fn get_index_spec(&self, index_name: String) -> Option<&IndexSpec<T>> {
        self.index_specs.get(&index_name)
    }

    fn contains(&self, index_name: &String) -> bool {
        self.index_specs.contains_key(index_name)
    }

    fn remove(&mut self, index_name: &String) {
        self.index_specs.remove(index_name);
    }
}

#[cfg(test)]
mod tests {
    use crate::extension::index::index::index_specs::{DefaultIndexSpecs, IndexSpecs};
    use crate::tests::primary_key_index_spec;

    #[test]
    fn add() {
        let mut specs = DefaultIndexSpecs::new();
        specs.add(primary_key_index_spec()).unwrap();
        assert!(specs.contains(&"metadata.name".to_string()));
    }

    #[test]
    fn add_with_exception() {
        let mut specs = DefaultIndexSpecs::new();
        let name_spec = primary_key_index_spec();
        specs.add(name_spec).unwrap();
        let name_spec = primary_key_index_spec();
        let result = specs.add(name_spec);
        assert!(result.is_err());
        match result {
            Err(e) => assert_eq!(
                "error:`IndexSpec with name metadata.name already exists`",
                e.to_string()
            ),
            _ => panic!("should be error"),
        }
    }

    #[test]
    fn get_index_specs() {
        let mut specs = DefaultIndexSpecs::new();
        specs.add(primary_key_index_spec()).unwrap();
        let index_specs = specs.get_index_specs();
        assert_eq!(1, index_specs.len());
    }

    #[test]
    fn get_index_spec() {
        let mut specs = DefaultIndexSpecs::new();
        specs.add(primary_key_index_spec()).unwrap();
        let index_spec = specs.get_index_spec("metadata.name".to_string());
        assert!(index_spec.is_some());
        let index_spec = index_spec.unwrap();

        // let any = index_spec.as_any();
        // assert!(any.is::<IndexSpec<FunctionalIndexAttribute<FakeExtension>>>());

        // let b = any
        //     .downcast_ref::<IndexSpec<FunctionalIndexAttribute<FakeExtension>>>()
        //     .unwrap();
        // let a = primary_key_index_spec();
        // assert_eq!(a, *b);
    }

    #[test]
    fn remove() {
        let mut specs = DefaultIndexSpecs::new();
        specs.add(primary_key_index_spec()).unwrap();
        assert!(specs.contains(&"metadata.name".to_string()));
        specs.remove(&"metadata.name".to_string());
        assert!(!specs.contains(&"metadata.name".to_string()));
    }
}
