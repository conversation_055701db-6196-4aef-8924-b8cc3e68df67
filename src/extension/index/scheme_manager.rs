use halo_model::{<PERSON><PERSON><PERSON><PERSON>, GroupVersionKind, GVK};
use once_cell::sync::Lazy;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::fmt::Display;
use std::slice::Iter;
use tokio::sync::Mutex;

use crate::config::app_error::{AppError, AppResult};
use crate::extension::index::index::index_spec_registry::IndexSpecRegistry;
use crate::extension::index::index::index_specs::{DefaultIndexSpecs, IndexSpecs};
use crate::extension::index::scheme_watcher_manager::{
    DefaultSchemeWatcherManager, SchemeRegistered, SchemeWatcher, SchemeWatcherManager,
};

pub static SCHEMES: Lazy<Mutex<Vec<Scheme>>> = Lazy::new(|| Mutex::new(Vec::new()));

#[derive(Debug, Serialize, Deserialize, <PERSON><PERSON>ult, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>h)]
pub struct Scheme {
    pub group_version_kind: GroupVersionKind,
    pub plural: String,
    pub singular: String,
    pub open_api_schema: Value,
}

impl Scheme {
    pub async fn new<T: GVK>() -> Scheme {
        Scheme {
            group_version_kind: GroupVersionKind {
                group: T::group().to_string(),
                version: T::version().to_string(),
                kind: T::kind().to_string(),
            },
            plural: T::plural().to_string(),
            singular: T::singular().to_string(),
            open_api_schema: Default::default(),
        }
    }
}

pub struct SchemeManager {
    pub schemes: Vec<Scheme>,
    pub watcher_manager: DefaultSchemeWatcherManager,
}

impl SchemeManager {
    pub fn new() -> SchemeManager {
        SchemeManager {
            schemes: Vec::new(),
            watcher_manager: DefaultSchemeWatcherManager::new(),
        }
    }
    fn register_scheme<T: ExtensionOperator + GVK>(&mut self, scheme: Scheme) -> AppResult<()> {
        if !self.schemes.contains(&scheme) {
            let mut index_spec_registry: IndexSpecRegistry<T> = IndexSpecRegistry::new();
            index_spec_registry.index_for(&scheme)?;
            self.schemes.push(scheme.clone());
            self.get_watchers()
                .for_each(|t| t.on_change(Box::new(SchemeRegistered::new(scheme.clone()))));
        }
        Ok(())
    }

    pub async fn register<T: ExtensionOperator + GVK>(&mut self) {
        let scheme = Self::convert_scheme::<T>();

        self.register_scheme::<T>(scheme);
    }

    pub async fn register_fun<F, T>(&mut self, f: F) -> AppResult<()>
    where
        F: Fn(&mut DefaultIndexSpecs<T>) -> AppResult<()>,
        T: ExtensionOperator + GVK,
    {
        let scheme = Self::convert_scheme::<T>();

        self.register_fn::<F, T>(scheme, f)
    }

    fn convert_scheme<T: ExtensionOperator + GVK>() -> Scheme {
        let scheme = Scheme {
            group_version_kind: GroupVersionKind {
                group: T::group().to_string(),
                version: T::version().to_string(),
                kind: T::kind().to_string(),
            },
            plural: T::plural().to_string(),
            singular: T::singular().to_string(),
            open_api_schema: Value::Null,
        };
        scheme
    }

    fn register_fn<F, T>(&mut self, scheme: Scheme, f: F) -> AppResult<()>
    where
        F: Fn(&mut DefaultIndexSpecs<T>) -> AppResult<()>,
        T: ExtensionOperator + GVK,
    {
        if self.schemes.contains(&scheme) {
            return Ok(());
        }
        let mut index_spec_registry: IndexSpecRegistry<T> = IndexSpecRegistry::new();

        index_spec_registry.index_for(&scheme)?;
        index_spec_registry.consumer::<F>(&scheme, f)?;
        self.schemes.push(scheme.clone());
        self.get_watchers()
            .for_each(|t| t.on_change(Box::new(SchemeRegistered::new(scheme.clone()))));
        Ok(())
    }

    pub fn unregister<T: ExtensionOperator>(&mut self, scheme: &Scheme) {
        if self.schemes.contains(scheme) {
            // self.index_spec_registry.remove_index_specs(scheme);
            // self.schemes.retain(|s| s != scheme);
            // self.get_watchers()
            //     .for_each(|t| t.on_change(Box::new(SchemeUnregistered::new(scheme.clone()))))
        }
    }

    pub fn schemes(&self) -> Iter<Scheme> {
        self.schemes.iter()
    }

    fn get_watchers(&self) -> Iter<Box<dyn SchemeWatcher>> {
        self.watcher_manager.watchers()
    }

    pub fn size(&self) -> usize {
        self.schemes.len()
    }

    pub fn fetch(&self, gvk: &GroupVersionKind) -> Option<&Scheme> {
        self.schemes().find(|t| t.group_version_kind == *gvk)
    }

    pub fn get(&self, gvk: &GroupVersionKind) -> AppResult<&Scheme> {
        self.fetch(gvk)
            .ok_or(AppError::SchemeNotFoundErr(gvk.clone()))
    }

    pub async fn get_by_type<T: GVK>(&self) -> AppResult<&Scheme> {
        self.get(&GroupVersionKind::new(
            T::group().to_string(),
            T::version().to_string(),
            T::kind().to_string(),
        ))
    }
}

pub async fn register<T: GVK>() {
    let scheme = Scheme {
        group_version_kind: GroupVersionKind {
            group: T::group().to_string(),
            version: T::version().to_string(),
            kind: T::kind().to_string(),
        },
        plural: T::plural().to_string(),
        singular: T::singular().to_string(),
        open_api_schema: Value::Null,
    };
    SCHEMES.lock().await.push(scheme);
}

pub async fn build_store_name_str<T: GVK>(name: &String) -> AppResult<String> {
    let store_name = SCHEMES
        .lock()
        .await
        .iter()
        .find(|scheme| scheme.struct_type == struct_type)
        .map(|scheme| build_store_name(scheme, name))
        .ok_or(anyhow::anyhow!("Scheme not found for : {}", struct_type).into());
    let kind = T::kind();

    store_name
}

pub async fn get_scheme<T: GVK>() -> AppResult<(String, Scheme)> {
    let group_version_kind = GroupVersionKind {
        group: T::group().to_string(),
        version: T::version().to_string(),
        kind: T::kind().to_string(),
    };

    let scheme = SchemeManager::get(&group_version_kind).await;

    let string = build_store_name_prefix(&scheme);

    let store_name = SCHEMES
        .lock()
        .await
        .iter()
        .find(|scheme| &scheme.struct_type == struct_type)
        .map(|scheme| (build_store_name(scheme, name), (*scheme).clone()))
        .ok_or(anyhow::anyhow!("Scheme not found for : {}", struct_type).into());
    store_name
}

pub async fn get_name_scheme(struct_type: &str) -> AppResult<(String, Scheme)> {
    let store_name = SCHEMES
        .lock()
        .await
        .iter()
        .find(|scheme| &scheme.struct_type == struct_type)
        .map(|scheme| (build_store_name_prefix(scheme), (*scheme).clone()))
        .ok_or(anyhow::anyhow!("Scheme not found for : {}", struct_type).into());
    store_name
}

pub fn build_store_name_prefix(scheme: &Scheme) -> String {
    // rule of key: /registry/[group]/plural-name/extension_index-name
    if scheme.group_version_kind.group.is_empty() {
        return format!("/registry/{}", scheme.plural,);
    }
    let key = format!(
        "/registry/{}/{}",
        scheme.group_version_kind.group, scheme.plural,
    );
    key
}
pub fn build_store_name(scheme: &Scheme, name: &String) -> String {
    // rule of key: /registry/[group]/plural-name/extension_index-name
    if scheme.group_version_kind.group.is_empty() {
        return format!("/registry/{}/{}", scheme.plural, name);
    }
    let key = format!(
        "/registry/{}/{}/{}",
        scheme.group_version_kind.group, scheme.plural, name
    );
    return key;
}

pub async fn init_scheme() {
    // register(String::from("user"), &user::GVK).await;
    // register(user::KIND, Arc::new(user::GVK.clone())).await;
    // register2(String::from("role"), &role::GVK).await;
}
