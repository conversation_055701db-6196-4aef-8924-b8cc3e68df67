use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct ListResult<T> {
    pub page: usize,
    pub size: usize,
    pub total: usize,
    pub items: Vec<T>,
}

impl<T: Clone> ListResult<T> {
    pub fn new(page: usize, size: usize, total: usize, items: Vec<T>) -> Self {
        ListResult {
            page,
            size,
            total,
            items,
        }
    }

    pub fn new_list() -> Self {
        ListResult::new(0, 0, 0, vec![])
    }

    pub fn sub_list(vec: Vec<T>, page: usize, size: usize) -> Vec<T> {
        let mut new_items = Vec::new();

        if size < 1 {
            return vec;
        }
        let total = vec.len();

        let page_start = if page == 1 { 0 } else { (page - 1) * size };
        let page_end = if page * size < total {
            page * size
        } else {
            total
        };
        if total > page_start {
            let new_items = vec[page_start..page_end].to_vec();
            return new_items;
        }
        new_items
    }
}
