use std::fmt::{<PERSON><PERSON><PERSON>, Formatter};
use serde::Serialize;
use crate::extension::index::selector::field_selector::FieldSelector;
use crate::extension::index::selector::label_selector::LabelSelector;

pub struct ListOptions {
    pub label_selector: LabelSelector,
    pub field_selector: FieldSelector,
}

impl Display for ListOptions {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        write!(f, "fieldSelector {} label_selector {} ", self.field_selector.query, self.label_selector)
    }
}

impl ListOptions {
    pub fn new(label_selector: LabelSelector, field_selector: FieldSelector) -> Self {
        ListOptions {
            label_selector,
            field_selector,
        }
    }
}
