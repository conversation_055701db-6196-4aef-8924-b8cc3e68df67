use macros::gvk;
use serde::{Deserialize, Serialize};
use std::collections::HashSet;


#[gvk(
    group = "",
    version = "v1alpha1",
    kind = "Menu",
    singular = "menu",
    plural = "menus"
)]
pub struct Menu {
    #[serde(rename = "spec")]
    pub spec: Spec,
}

#[derive(<PERSON>bu<PERSON>, <PERSON>lone, Serialize, Deserialize, Default)]
pub struct Spec {
    #[serde(rename = "displayName")]
    pub display_name: String,

    #[serde(rename = "menuItems")]
    pub menu_items: HashSet<String>,
}