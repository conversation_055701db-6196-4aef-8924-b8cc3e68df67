use std::collections::BTreeMap;

use std::hash::Hash;

use macros::gvk;

pub static KIND: &str = "ConfigMap";

#[gvk(
    group = "",
    version = "v1alpha1",
    kind = "ConfigMap",
    plural = "configmaps",
    singular = "configmap"
)]
pub struct ConfigMap {
    #[serde(rename = "data")]
    pub data: Option<BTreeMap<String, String>>,
}

impl ConfigMap {
    pub fn new(data: Option<BTreeMap<String, String>>) -> Self {
        let config_map = ConfigMap::new();
        config_map.data = data;
        config_map
    }
}
