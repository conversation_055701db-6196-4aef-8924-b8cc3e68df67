use macros::gvk;
use serde::{Deserialize, Serialize};

pub static KIND: &str = "Attachment";
pub static GROUP: &str = "storage.halo.run";


#[gvk(
    group = "halo.run",
    version = "v1alpha1",
    kind = "Attachment",
    plural = "attachments",
    singular = "attachment"
)]
pub struct Attachment {
    pub spec: AttachmentSpec,
    pub status: AttachmentStatus,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct AttachmentSpec {
    pub display_name: String,
    pub group_name: String,
    pub policy_name: String,
    pub owner_name: String,
    pub media_type: String,
    pub size: i64,
    pub tags: Vec<String>,

}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct AttachmentStatus {
    pub permalink: String,
    pub thumbnails: Vec<String>,
}