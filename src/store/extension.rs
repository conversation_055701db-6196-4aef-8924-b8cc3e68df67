//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "extensions")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub name: String,
    // #[sea_orm(column_type = "Binary")]
    pub data: Vec<u8>,
    pub version: i64,
}


#[derive(<PERSON><PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
