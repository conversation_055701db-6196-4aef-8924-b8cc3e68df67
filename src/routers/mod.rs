use salvo::{
    prelude::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>},
    Router,
};

use self::{
    demo::hello,
    user::{
        get_user_by_name, get_user_permission, grant_permission, me, update_profile,
    },
};

pub mod demo;
pub mod user;

pub fn router() -> Router {
    let mut no_auth_routers = vec![
        // Router::with_path("/api/login").post(post_login),
        Router::with_path("/users/-").get(me),
        Router::with_path("/users/<name>").get(get_user_by_name),
        Router::with_path("/users/-").put(update_profile),
        Router::with_path("/users/<name>/permissions").post(grant_permission),
        Router::with_path("/users/<name>/permissions").get(get_user_permission),
    ];

    // let mut need_auth_routers = vec![
    //     Router::with_path("/api/users").get(get_users)
    //     .post(post_add_user)
    //     .push(
    //         Router::with_path("<id>")
    //             .put(put_update_user)
    //             .delete(delete_user),
    //     ),
    // ];
    let router = Router::new()
        .hoop(Logger::new())
        .hoop(CatchPanic::new())
        .get(hello)
        .append(&mut no_auth_routers)
        // .push(
        //     Router::new()
        //         .append(&mut need_auth_routers)
        //         .hoop(jwt_hoop()),
        // )
        ;
    let doc = OpenApi::new("salvo web api", "0.0.1").merge_router(&router);
    router
        .push(doc.into_router("/api-doc/openapi.json"))
        .push(SwaggerUi::new("/api-doc/openapi.json").into_router("swagger-ui"))
}
