use serde::{Deserialize, Serialize};

pub static SYSTEM_CONFIG_DEFAULT: &str = "system-default";
pub static SYSTEM_CONFIG: &str = "system";

pub static USER_GROUP: &str = "user";

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct User {
    pub allow_registration: bool,
    pub must_verify_email_on_registration: bool,
    pub default_role: String,
    pub avatar_policy: String,
    pub uc_attachment_policy: String,
}