use halo_model::ExtensionOperator;
use index_view_data_set::FakeExtension;

use crate::extension::index::index::query::{
    index_attribute::IndexAttributeFactory,
    index_spec::{IndexSpec, OrderType},
};

pub mod index_view_data_set;
pub mod extension;
mod macros_test;

pub fn contains_entry<'a, I>(mut iter: I, key: &str, value: &str) -> bool
where
    I: Iterator<Item = (&'a String, &'a String)>,
{
    iter.any(|(k, v)| k == key && v == value)
}

pub fn primary_key_index_spec() -> IndexSpec<FakeExtension> {
    IndexSpec::new(
        "metadata.name".to_string(),
        OrderType::ASC,
        true,
        IndexAttributeFactory::simple_attribute(
            "FakeExtension".to_string(),
            |e: &FakeExtension| e.get_metadata().get_name(),
        ),
    )
}

#[cfg(test)]
mod tests {
    use salvo::prelude::*;
    use salvo::test::{ResponseExt, TestClient};

    use crate::config::CFG;

    #[tokio::test]
    async fn test_hello_world() {
        let service = Service::new(crate::router());

        let content = TestClient::get(format!(
            "http://{}",
            &CFG.server.address.replace("0.0.0.0", "127.0.0.1")
        ))
        .send(&service)
        .await
        .take_string()
        .await
        .unwrap();
        assert_eq!(content, "Hello World from salvo");
    }
}
