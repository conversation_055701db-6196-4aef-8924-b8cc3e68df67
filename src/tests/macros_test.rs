use macros::gvk;
use pretty_assertions::assert_eq;
use serde::{Deserialize, Serialize};

// 定义应用特定的资源
#[gvk(
    group = "app.group",
    version = "v1",
    kind = "AppResource",
    plural = "appresources",
    singular = "appresource"
)]
#[derive(Debug, Default, Serialize, Deserialize, PartialEq)]
struct AppResource {
    config: String,
    enabled: bool,
}

#[test]
fn test_app_resource_behavior() {
    let mut resource = AppResource {
        config: "default".to_string(),
        enabled: true,
        ..Default::default()
    };

    // 验证 GVK 实现
    assert_eq!(AppResource::api_version(), "app.group/v1");

    // 修改元数据
    resource.metadata_mut().set_name("app-resource-1".to_string());

    // 序列化
    let json = serde_json::to_string(&resource).unwrap();
    assert!(json.contains("\"apiVersion\":\"app.group/v1\""));
    assert!(json.contains("\"config\":\"default\""));

    // 反序列化
    let deserialized: AppResource = serde_json::from_str(&json).unwrap();
    assert_eq!(resource, deserialized);

    // 测试业务逻辑
    resource.config = "custom".to_string();
    assert_eq!(resource.config, "custom");
}

#[test]
fn test_resource_list() {
    #[gvk(
        group = "app.group",
        version = "v1",
        kind = "ResourceList",
        plural = "resourcelists",
        singular = "resourcelist"
    )]
    #[derive(Debug, Default)]
    struct ResourceList {
        items: Vec<AppResource>,
    }

    let list = ResourceList {
        items: vec![
            AppResource {
                config: "first".to_string(),
                ..Default::default()
            },
            AppResource {
                config: "second".to_string(),
                ..Default::default()
            },
        ],
        ..Default::default()
    };

    // 验证 GVK
    assert_eq!(ResourceList::kind(), "ResourceList");

    // 验证项目数量
    assert_eq!(list.items.len(), 2);

    // 验证序列化
    let json = serde_json::to_string(&list).unwrap();
    assert!(json.contains("\"items\":["));
}